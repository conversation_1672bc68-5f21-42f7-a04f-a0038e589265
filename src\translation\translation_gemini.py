import os
from typing import Optional, Sequence, Mapping, Set, Tuple
from src.utils.logger import logger


class GeminiTranslation:
    """
    provides translation functionality using google's gemini api.

    this src handles all translation tasks using the gemini model.
    """

    DEFAULT_MODEL_NAME = "gemini-2.5-flash-preview-05-20"

    def __init__(
        self,
        rate_limiter=None,
        model_name: str | None = None,
        translation_workers: int = 20,
    ):
        self._api_key = self._get_api_key()
        self._genai = self._configure_gemini()
        self._pairs = None
        self._rate_limiter = rate_limiter
        self._model_name = model_name or self.DEFAULT_MODEL_NAME
        self._translation_workers = translation_workers

    @staticmethod
    def _get_api_key() -> Optional[str]:
        """get the google api key from environment variables."""
        api_key = os.getenv("google_api_key")
        if not api_key:
            logger().warning(
                "google_api_key environment variable not set. translation will be limited."
            )
        return api_key

    def _configure_gemini(self):
        """configure the gemini api with the api key."""
        import google.generativeai as genai

        if not self._api_key:
            return None

        genai.configure(api_key=self._api_key)
        return genai

    def load_model(self):
        """
        load the translation model. for gemini, this just verifies api key is set.
        """
        if not self._api_key:
            logger().warning(
                "no google api key found. make sure to set google_api_key environment variable."
            )
        else:
            logger().info("gemini api key configured successfully")

    def get_language_pairs(self) -> Set[Tuple[str, str]]:
        """
        gemini can handle all language pairs, so we return an empty set
        as a signal that any language pair is supported.
        """
        # returning an empty set since gemini can handle any language pair
        # the check_languages function has been updated to handle this case
        return set()

    def _generate_script(self, *, utterance_metadata, key: str = "text") -> str:
        """generates a script string from a list of utterance metadata."""
        _BREAK_MARKER = "<break>"
        trimmed_lines = [
            item[key].strip() if item[key] else "" for item in utterance_metadata
        ]
        logger().debug(f"translation.generate_script. input: {trimmed_lines}")
        r = _BREAK_MARKER + _BREAK_MARKER.join(trimmed_lines) + _BREAK_MARKER
        logger().debug(f"translation.generate_script. returns: {r}")
        return r

    def translate(self, source_language: str, target_language: str, text: str) -> str:
        """
        translate text from source language to target language.

        handles special cases for uzbek and russian to english.
        """
        # special case for uzbek
        if target_language == "uz" or target_language == "uzb":
            # use english as intermediate language for translation to uzbek
            # this is a workaround since most models don't support uzbek directly
            logger().debug(
                f"translate: using special uzbek handler for text: {text[:50]}..."
            )
            return self._translate_text_to_uzbek(source_language, text)
        # special case for russian to english
        elif (source_language == "rus" or source_language == "ru") and (
            target_language == "en" or target_language == "eng"
        ):
            logger().debug(
                f"translate: using special russian to english handler for text: {text[:50]}..."
            )
            return self._translate_text_russian_to_english(text)
        else:
            logger().debug(
                f"translate: using standard translation flow for {source_language} to {target_language}"
            )
            return self._translate_text(source_language, target_language, text)

    def _translate_text(
        self, source_language: str, target_language: str, text: str
    ) -> str:
        """
        translate text from source language to target language using gemini's translation capabilities.

        args:
            source_language: source language code
            target_language: target language code
            text: text to translate

        returns:
            translated text
        """

        if self._rate_limiter is not None:
            self._rate_limiter.wait()

        if not self._genai or not text.strip():
            return text

        try:
            # set up the model
            model = self._genai.GenerativeModel(self._model_name)

            # create the prompt for translation
            prompt = f"""
              you are an expert literary translator. translate the following text from {source_language} to {target_language}. your translation should prioritize the following:

              *   **accuracy:** semantic and contextual equivalence. precise rendering of specialized terminology.
              *   **artistry:** recreate rhythm, cadence, and flow. adapt sentence structures creatively for readability.
              *   **style:** emulate the author's stylistic devices (e.g., stream of consciousness, alliteration, irony).
              *   **cultural nuance:** localize untranslatable elements with analogous expressions. replace culture-specific proverbs with equivalents.
              *   **refinement:** ensure elegance and naturalness. avoid stiff or overly academic phrasing.
              *   **alignment:** no thematic or narrative deviations. metaphors and imagery should evoke the same response.
              *   **quality assurance:** eliminate omissions, inaccuracies, or tonal inconsistencies.

              **important notes:**
              *   for dialogue, prioritize natural speech patterns in {target_language}.
              *   preserve deliberate stylistic choices (repetition, fragmented syntax) unless they hinder comprehension.
              *   return **only** the translated text. do **not** include any labels, comments, or other extraneous text.

              text to translate:
              ```
              {text}
              ```
            """

            # generate the translation
            response = model.generate_content(prompt)

            # extract and return the translated text
            if response and hasattr(response, "text"):
                translated_text = response.text.strip()
                logger().info(
                    f"successfully translated from {source_language} to {target_language} using gemini: ['{translated_text}']"
                )
                return translated_text
            else:
                logger().warning("unexpected response format from gemini api")
                return text

        except Exception as e:
            logger().error(f"error using gemini api for translation: {str(e)}")
            return text  # fallback to original text on error

    def _translate_text_to_uzbek(self, source_language: str, text: str) -> str:
        """
        special handler for uzbek translation using google's gemini model.

        args:
            source_language: source language code
            text: text to translate

        returns:
            translated text in uzbek (using latin script)
        """
        return self.translate_to_uzbek(source_language, text)

    def _translate_text_russian_to_english(self, text: str) -> str:
        """
        special handler for russian to english translation using google's gemini model.

        args:
            text: russian text to translate

        returns:
            translated text in english
        """
        return self.translate_russian_to_english(text)

    @staticmethod
    def translate_to_uzbek(source_language: str, text: str) -> str:
        """
        translate text to uzbek using gemini api.

        args:
            source_language: source language code
            text: text to translate

        returns:
            translated text in uzbek (using latin script)
        """
        logger().info("using gemini for uzbek translation")

        try:
            import google.generativeai as genai
        except ImportError:
            logger().error(
                "google generative ai package not found. install it with: pip install google-generativeai"
            )
            return text

        api_key = GeminiTranslation._get_api_key()
        if not api_key or not text.strip():
            return text

        try:
            # configure the api
            genai.configure(api_key=api_key)

            # set up the model
            model = genai.GenerativeModel(GeminiTranslation.DEFAULT_MODEL_NAME)

            # create the prompt for translation
            prompt = f"""
            you are an expert literary translator. translate the following text from {source_language} to uzbek. your translation should prioritize the following:

            *   **accuracy:** semantic and contextual equivalence. precise rendering of specialized terminology.
            *   **artistry:** recreate rhythm, cadence, and flow. adapt sentence structures creatively for readability.
            *   **style:** emulate the author's stylistic devices (e.g., stream of consciousness, alliteration, irony).
            *   **cultural nuance:** localize untranslatable elements with analogous expressions. replace culture-specific proverbs with equivalents.
            *   **refinement:** ensure elegance and naturalness. avoid stiff or overly academic phrasing.
            *   **alignment:** no thematic or narrative deviations. metaphors and imagery should evoke the same response.
            *   **quality assurance:** eliminate omissions, inaccuracies, or tonal inconsistencies.

            **important notes:**

            *   write the translation in **latin script** only.
            *   for dialogue, prioritize natural speech patterns in uzbek.
            *   preserve deliberate stylistic choices (repetition, fragmented syntax) unless they hinder comprehension.
            *   return **only** the translated text. do **not** include any labels, comments, or other extraneous text.

            text to translate:
            ```
            {text}
            ```
            """

            # generate the translation
            response = model.generate_content(prompt)

            # extract and return the translated text
            if response and hasattr(response, "text"):
                translated_text = response.text.strip()
                logger().info(
                    f"successfully translated to uzbek using gemini: ['{translated_text}']"
                )
                return translated_text
            else:
                logger().warning("unexpected response format from gemini api")
                return text

        except Exception as e:
            logger().error(f"error using gemini api for translation: {str(e)}")
            return text  # fallback to original text on error

    @staticmethod
    def translate_russian_to_english(text: str) -> str:
        """
        translate text from russian to english using gemini api.

        args:
            text: russian text to translate

        returns:
            translated text in english
        """
        logger().info("using gemini for russian to english translation")

        import google.generativeai as genai

        api_key = GeminiTranslation._get_api_key()
        if not api_key or not text.strip():
            return text

        try:
            # configure the api
            genai.configure(api_key=api_key)

            # set up the model
            model = genai.GenerativeModel(GeminiTranslation.DEFAULT_MODEL_NAME)

            # create the prompt for translation
            prompt = f"""
            you are an expert literary translator. translate the following text from russian to english. your translation should prioritize the following:

            *   **accuracy:** semantic and contextual equivalence. precise rendering of specialized terminology.
            *   **artistry:** recreate rhythm, cadence, and flow. adapt sentence structures creatively for readability.
            *   **style:** emulate the author's stylistic devices (e.g., stream of consciousness, alliteration, irony).
            *   **cultural nuance:** localize untranslatable elements with analogous expressions. replace culture-specific proverbs with equivalents.
            *   **refinement:** ensure elegance and naturalness. avoid stiff or overly academic phrasing.
            *   **alignment:** no thematic or narrative deviations. metaphors and imagery should evoke the same response.
            *   **quality assurance:** eliminate omissions, inaccuracies, or tonal inconsistencies.

            **important notes:**
            *   for dialogue, prioritize natural speech patterns in english.
            *   preserve deliberate stylistic choices (repetition, fragmented syntax) unless they hinder comprehension.
            *   return **only** the translated text. do **not** include any labels, comments, or other extraneous text.

            text to translate:
            ```
            {text}
            ```
            """

            # generate the translation
            response = model.generate_content(prompt)

            # extract and return the translated text
            if response and hasattr(response, "text"):
                translated_text = response.text.strip()
                logger().info(
                    f"successfully translated from russian to english using gemini: ['{translated_text}']"
                )
                return translated_text
            else:
                logger().warning("unexpected response format from gemini api")
                return text

        except Exception as e:
            logger().error(f"error using gemini api for translation: {str(e)}")
            return text  # fallback to original text on error

    def translate_utterances(
        self,
        *,
        utterance_metadata: Sequence[Mapping[str, str | float]],
        source_language: str,
        target_language: str,
    ) -> Sequence[Mapping[str, str | float]]:
        """
        translate a sequence of utterance metadata.
        """
        script = self._generate_script(utterance_metadata=utterance_metadata)
        translated_script = self._translate_script(
            script=script,
            source_language=source_language,
            target_language=target_language,
        )
        return self._add_translations(
            utterance_metadata=utterance_metadata,
            translated_script=translated_script,
        )

    def _translate_script(
        self,
        *,
        script: str,
        source_language: str,
        target_language: str,
    ) -> str:
        """
        translates the provided transcript to the target language.
        """
        _BREAK_MARKER = "<break>"
        import time
        import json

        start_time = time.time()

        logger().debug(f"translation.translate_script. input script: {script}")
        logger().debug(
            f"translation.translate_script. input source_language: {source_language}, target_language: {target_language}"
        )

        # special handling for uzbek language
        is_uzbek = target_language == "uz" or target_language == "uzb"
        # special handling for russian to english translation
        is_russian_to_english = (
            source_language == "rus" or source_language == "ru"
        ) and (target_language == "en" or target_language == "eng")

        if is_uzbek:
            logger().info("using special uzbek translation flow in _translate_script")
        elif is_russian_to_english:
            logger().info(
                "using special russian to english translation flow in _translate_script"
            )

        # split the input string by the <break> delimiter
        parts = script.split(_BREAK_MARKER)

        # filter out empty parts and keep track of original indices
        non_empty_parts = []
        part_indices = []
        for i, text in enumerate(parts):
            if len(text.strip()) > 0:
                non_empty_parts.append(text)
                part_indices.append(i)

        logger().info(f"translating {len(non_empty_parts)} text segments in parallel")

        # determine optimal number of workers based on configuration and content
        # with 1k rpm, we can safely use more workers
        max_workers = min(self._translation_workers, len(non_empty_parts))
        logger().info(
            f"using {max_workers} parallel workers for translation (configured max: {self._translation_workers})"
        )

        def translate_single_text(text: str) -> str:
            """translate a single text segment with proper error handling."""
            try:
                if is_uzbek:
                    return self._translate_text_to_uzbek(
                        source_language=source_language,
                        text=text,
                    )
                elif is_russian_to_english:
                    return self._translate_text_russian_to_english(text)
                else:
                    return self._translate_text(
                        source_language=source_language,
                        target_language=target_language,
                        text=text,
                    )
            except Exception as e:
                logger().error(f"error translating text segment: {str(e)}")
                return text  # fallback to original text

        # use threadpoolexecutor for parallel translation
        translated_parts = [""] * len(parts)  # initialize with empty strings

        if non_empty_parts:
            import concurrent.futures

            parallel_start_time = time.time()
            with concurrent.futures.ThreadPoolExecutor(
                max_workers=max_workers
            ) as executor:
                # submit all translation tasks
                future_to_index = {
                    executor.submit(translate_single_text, text): (text, original_idx)
                    for text, original_idx in zip(non_empty_parts, part_indices)
                }

                # collect results as they complete
                completed_count = 0
                for future in concurrent.futures.as_completed(future_to_index):
                    text, original_idx = future_to_index[future]
                    try:
                        translation = future.result()
                        translated_parts[original_idx] = translation
                        completed_count += 1
                        logger().debug(
                            f"completed translation for segment {original_idx} ({completed_count}/{len(non_empty_parts)})"
                        )
                    except Exception as e:
                        logger().error(
                            f"failed to get translation result for segment {original_idx}: {str(e)}"
                        )
                        translated_parts[original_idx] = text  # fallback to original
                        completed_count += 1

            parallel_time = time.time() - parallel_start_time
            logger().info(
                f"parallel translation completed in {parallel_time:.2f} seconds"
            )

        translation = _BREAK_MARKER.join(translated_parts)
        logger().debug(f"translation.translate_script. translation: {translation}")

        pretty_data = json.dumps(translation, indent=4, ensure_ascii=False)
        logger().debug(f"translation.translate_script. returns: {pretty_data}")
        execution_time = time.time() - start_time
        logger().debug(
            "translation.translate_script. time: %.2f seconds.", execution_time
        )

        return translation

    def _add_translations(
        self,
        *,
        utterance_metadata: Sequence[Mapping[str, str | float]],
        translated_script: str,
    ) -> Sequence[Mapping[str, str | float]]:
        """
        updates the "translated_text" field of each utterance metadata with the corresponding text segment.
        """
        import re
        import json

        _BREAK_MARKER = "<break>"

        stripped_translation = re.sub(
            rf"^\s*{_BREAK_MARKER}\s*|\s*{_BREAK_MARKER}\s*$", "", translated_script
        )
        if stripped_translation:
            text_segments = stripped_translation.split(_BREAK_MARKER)
        else:
            text_segments = []

        if len(utterance_metadata) != len(text_segments):
            raise ValueError(
                "the utterance metadata must be of the same length as the text"
                f" segments. currently they are: {len(utterance_metadata)} and"
                f" {len(text_segments)}."
            )
        updated_utterance_metadata = []
        for metadata, translated_text in zip(utterance_metadata, text_segments):
            updated_utterance_metadata.append(
                {**metadata, "translated_text": translated_text}
            )

        pretty_data = json.dumps(
            updated_utterance_metadata, indent=4, ensure_ascii=False
        )
        logger().debug(f"translation.translated_script. input: {translated_script}")
        logger().debug(f"translation.translated_script. returns: {pretty_data}")
        return updated_utterance_metadata
