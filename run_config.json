{"_comment": "aizen platform dubbing configuration - assemblyai example", "_description": "this configuration demonstrates using assemblyai for speech-to-text with advanced features like speaker diarization and language detection.", "input": {"_comment": "input media processing settings", "source_path": "https://inspired.fra1.cdn.digitaloceanspaces.com/aizen/doctor_short.mp4", "source_format": "mp4", "temp_dir": "./tmp", "silence": {"_comment": "silence detection for natural speech segmentation", "method": "energy", "threshold": 0.35, "padding_seconds": 0.5}, "separation": {"_comment": "audio source separation using demucs - htdemucs_ft provides best quality but is 4x slower than htdemucs", "model": "htdemucs_ft", "stems": ["vocals", "accompaniment"], "output_format": "wav", "segment_duration": 7.8, "overlap": 0.25, "shifts": 10, "split": true, "device": "cuda", "jobs": 1, "clip_mode": "rescale", "float32": false, "int24": false, "two_stems": null, "extra_args": []}}, "translation": {"_comment": "translation pipeline configuration using google gemini models", "source_lang": "eng", "target_lang": "uzb", "analyzer": {"_comment": "gemini-2.5-flash is faster and cheaper than gemini-2.5-pro while maintaining good quality", "provider": "gemini", "model": "gemini-2.5-flash", "temperature": 0.2, "top_p": 0.95}, "segment_processor": {"_comment": "same model for consistency - use gemini-2.5-pro for highest quality if budget allows", "provider": "gemini", "model": "gemini-2.5-flash", "temperature": 0.2, "top_p": 0.95}}, "processing": {"_comment": "audio processing and speech analysis configuration", "skip_diarization": false, "enhanced_segmentation": true, "max_segment_duration": 30.0, "update_mode": false, "run_id": null}, "performance": {"_comment": "resource management and processing limits", "max_workers": 4, "rate_limit_per_sec": 2, "use_gpu": true, "max_memory_gb": null}, "models": {"_comment": "text-to-speech and transcription model settings - using assemblyai for advanced stt features", "tts_provider": "azure", "stt_provider": "assemblyai", "transcription_chunk_size": 30.0, "timestamp_window": 0.5, "tts_voice": "uz-UZ-MadinaNeural", "temperature": 0.7, "target_language_region": "", "hugging_face_token": null}, "output": {"_comment": "output file configuration and logging settings - debug level provides detailed information for development and troubleshooting", "output_dir": "./outputs", "naming_pattern": "{basename}_{lang}.{ext}", "log_level": "debug", "log_format": "%(asctime)s | %(levelname)s | %(message)s", "video_encoder": "libx264", "original_subtitles": false, "dubbed_subtitles": false, "clean_intermediate_files": false}}