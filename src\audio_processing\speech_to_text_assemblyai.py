import array
import os
import re
import tempfile
import urllib.parse
import urllib.request
from typing import Mapping, Sequence

import assemblyai as aai
from iso639 import Lang
from pydub import AudioSegment

from src.audio_processing.voice_gender_classifier import VoiceGenderClassifier
from src.utils.logger import logger


class SpeechToTextAssemblyAI:
    """assemblyai speech-to-text implementation with advanced features."""

    def __init__(
        self,
        *,
        model_name="best",  # assemblyai speech model (best, nano, slam-1, universal)
        device="cpu",  # assemblyai is cloud-based, device doesn't matter
        cpu_threads=0,  # not applicable for cloud service
        api_key: str | None = None,
        speaker_labels: bool = True,  # enable speaker diarization
        auto_highlights: bool = False,  # extract key phrases
        sentiment_analysis: bool = False,  # analyze sentiment
        entity_detection: bool = False,  # detect entities
        language_detection: bool = True,  # auto-detect language
    ):
        # initialize base attributes
        self.model_name = model_name
        self._model = None
        self.device = device
        self.cpu_threads = cpu_threads
        self.MIN_SECS = 0.5

        # get api key from parameter or environment
        self.api_key = api_key or os.environ.get("ASSEMBLYAI_API_KEY")
        if not self.api_key:
            logger().warning(
                "no assemblyai api key found. set assemblyai_api_key environment variable "
                "or pass api_key parameter. running in test mode."
            )
            self.api_key = "test_key"

        # configure assemblyai sdk
        if self.api_key != "test_key":
            aai.settings.api_key = self.api_key

        # assemblyai-specific features
        self.speaker_labels = speaker_labels
        self.auto_highlights = auto_highlights
        self.sentiment_analysis = sentiment_analysis
        self.entity_detection = entity_detection
        self.language_detection = language_detection

        # map model names to assemblyai speech models
        self.speech_model_map = {
            "best": aai.SpeechModel.best,
            "nano": aai.SpeechModel.nano,
            "slam-1": aai.SpeechModel.slam_1,
            "universal": aai.SpeechModel.universal,
        }

        # supported languages (assemblyai supports many languages)
        self._supported_languages = [
            "eng",
            "spa",
            "fra",
            "deu",
            "ita",
            "por",
            "rus",
            "jpn",
            "kor",
            "zho",
            "ara",
            "hin",
            "tur",
            "pol",
            "nld",
            "swe",
            "dan",
            "nor",
            "fin",
            "ces",
            "hun",
            "ron",
            "bul",
            "hrv",
            "slk",
            "slv",
            "est",
            "lav",
            "lit",
            "mlt",
            "ell",
            "heb",
            "tha",
            "vie",
            "ind",
            "msa",
            "tgl",
            "ukr",
            "bel",
            "kaz",
            "uzb",
            "aze",
            "kat",
            "hye",
            "fas",
            "urd",
            "ben",
            "guj",
            "pan",
            "tam",
            "tel",
            "kan",
            "mal",
            "ori",
            "mar",
            "nep",
            "sin",
            "mya",
            "khm",
            "lao",
        ]

        logger().info(
            f"initialized assemblyai stt with features: speaker_labels={speaker_labels}"
        )

    @property
    def model(self):
        return self._model

    @model.setter
    def model(self, value):
        self._model = value

    def _get_iso_639_1(self, iso_639_3: str):
        o = Lang(iso_639_3)
        iso_639_1 = o.pt1
        return iso_639_1

    def _get_iso_639_3(self, iso_639_1: str):
        if iso_639_1 == "jw":
            iso_639_1 = "jv"

        o = Lang(iso_639_1)
        iso_639_3 = o.pt3
        return iso_639_3

    def _make_sure_single_space(self, sentence: str) -> str:
        """whisper sometimes includes spaces at the beginning of sentences or multiple spaces between words"""
        fixed = re.sub(r"\s{2,}", " ", sentence)
        fixed = fixed.strip()
        return fixed

    def _is_short_audio(self, duration: float) -> bool:
        return duration < self.MIN_SECS

    def load_model(self):
        """assemblyai is cloud-based, no local model loading required."""
        try:
            if self.api_key != "test_key":
                # verify api key by creating a transcriber instance
                self.transcriber = aai.Transcriber()
                logger().info("assemblyai api connection verified successfully")
            else:
                logger().warning(
                    "running assemblyai in test mode - api calls will be mocked"
                )
                self.transcriber = None

            self._model = f"assemblyai_{self.model_name}"

        except Exception as e:
            logger().error(f"failed to initialize assemblyai: {e}")
            raise

    def get_languages(self):
        """return list of supported languages in iso 639-3 format."""
        return self._supported_languages.copy()

    def detect_language(self, filename: str) -> str:
        """detect language from audio/video file, handling both local files and urls."""
        duration_secs = 30

        # handle urls by downloading them to a temporary file
        temp_file = None
        actual_filename = filename

        if filename.startswith(("http://", "https://", "ftp://", "ftps://")):
            logger().info(
                f"downloading video from url for language detection: {filename}"
            )

            # create a temporary file
            parsed_url = urllib.parse.urlparse(filename)
            file_ext = os.path.splitext(parsed_url.path)[1] or ".mp4"
            temp_file = tempfile.NamedTemporaryFile(suffix=file_ext, delete=False)
            temp_file.close()

            try:
                # download the file
                urllib.request.urlretrieve(filename, temp_file.name)
                actual_filename = temp_file.name
                logger().info(f"downloaded video to temporary file: {actual_filename}")
            except Exception as e:
                logger().error(f"failed to download video from url: {e}")
                if os.path.exists(temp_file.name):
                    os.unlink(temp_file.name)
                raise

        try:
            audio = AudioSegment.from_file(actual_filename)
            audio = audio.set_channels(1)
            audio = audio.set_frame_rate(16000)

            first_seconds = audio[: duration_secs * 1000].get_array_of_samples()
            return self._get_audio_language(first_seconds)
        finally:
            # clean up temporary file if it was created
            if temp_file and os.path.exists(temp_file.name):
                try:
                    os.unlink(temp_file.name)
                    logger().info(f"cleaned up temporary file: {temp_file.name}")
                except Exception as e:
                    logger().warning(f"failed to clean up temporary file: {e}")

    def _transcribe(
        self,
        *,
        vocals_filepath: str,
        source_language_iso_639_1: str,
    ) -> str:
        """transcribe audio file using assemblyai."""
        if self.api_key == "test_key":
            logger().warning("using test mode - returning placeholder transcription")
            return "this is a test transcription from assemblyai"

        try:
            # get the speech model to use
            speech_model = self.speech_model_map.get(
                self.model_name, aai.SpeechModel.best
            )

            # configure transcription settings based on assemblyai api
            config = aai.TranscriptionConfig(
                speech_model=speech_model,
                language_code=(
                    source_language_iso_639_1 if not self.language_detection else None
                ),
                language_detection=self.language_detection,
                speaker_labels=self.speaker_labels,
                auto_highlights=self.auto_highlights,
                sentiment_analysis=self.sentiment_analysis,
                entity_detection=self.entity_detection,
                punctuate=True,
                format_text=True,
            )

            transcriber = aai.Transcriber(config=config)

            logger().info(f"starting assemblyai transcription for: {vocals_filepath}")
            transcript = transcriber.transcribe(vocals_filepath)

            # the sdk handles polling automatically, but we can check status
            if transcript.status == aai.TranscriptStatus.error:
                logger().error(f"assemblyai transcription failed: {transcript.error}")
                return ""

            logger().info("assemblyai transcription completed successfully")

            # store additional data for potential future use
            if hasattr(transcript, "utterances") and transcript.utterances:
                logger().debug(
                    f"transcript contains {len(transcript.utterances)} utterances"
                )

            # ensure we have text before processing
            text = transcript.text or ""
            return self._make_sure_single_space(text)

        except Exception as e:
            logger().error(f"assemblyai transcription error: {e}")
            # fallback to empty string to prevent pipeline failure
            return ""

    def _get_audio_language(self, audio: array.array) -> str:
        """detect language from audio sample using assemblyai."""
        if self.api_key == "test_key":
            logger().warning("using test mode - returning default language")
            return "eng"

        try:
            # convert audio array to temporary file for assemblyai
            audio_segment = AudioSegment(
                audio.tobytes(), frame_rate=16000, sample_width=2, channels=1
            )

            # create temporary file
            temp_file = "temp_language_detection.wav"
            audio_segment.export(temp_file, format="wav")

            try:
                config = aai.TranscriptionConfig(
                    language_detection=True,
                    speaker_labels=False,
                )

                transcriber = aai.Transcriber(config=config)
                transcript = transcriber.transcribe(temp_file)

                if transcript.status == aai.TranscriptStatus.completed:
                    # assemblyai returns language detection in the transcript
                    detected_language = getattr(transcript, "language_code", None)
                    if detected_language:
                        # convert iso 639-1 to iso 639-3
                        iso_639_3 = self._get_iso_639_3(detected_language)
                        logger().debug(f"assemblyai detected language: {iso_639_3}")
                        return iso_639_3
                else:
                    logger().warning(
                        "assemblyai language detection failed, defaulting to english"
                    )
                    return "eng"

            finally:
                # cleanup temporary file
                if os.path.exists(temp_file):
                    os.remove(temp_file)

            # fallback if no language detected
            return "eng"

        except Exception as e:
            logger().error(f"assemblyai language detection error: {e}")
            return "eng"  # fallback to english

    def get_speaker_utterances(
        self, vocals_filepath: str, source_language_iso_639_1: str
    ):
        """get detailed utterance information with speaker labels from assemblyai."""
        if self.api_key == "test_key":
            logger().warning("speaker utterances not available in test mode")
            return []

        try:
            # get the speech model to use
            speech_model = self.speech_model_map.get(
                self.model_name, aai.SpeechModel.best
            )

            config = aai.TranscriptionConfig(
                speech_model=speech_model,
                language_code=(
                    source_language_iso_639_1 if not self.language_detection else None
                ),
                language_detection=self.language_detection,
                speaker_labels=True,  # force speaker labels for this method
                punctuate=True,
                format_text=True,
            )

            transcriber = aai.Transcriber(config=config)
            transcript = transcriber.transcribe(vocals_filepath)

            if (
                transcript.status == aai.TranscriptStatus.completed
                and transcript.utterances
            ):
                utterances = []
                for utterance in transcript.utterances:
                    utterances.append(
                        {
                            "speaker": utterance.speaker,
                            "text": self._make_sure_single_space(utterance.text),
                            "start": utterance.start / 1000.0,  # convert ms to seconds
                            "end": utterance.end / 1000.0,
                            "confidence": getattr(utterance, "confidence", 0.9),
                        }
                    )

                logger().info(f"extracted {len(utterances)} speaker utterances")
                return utterances
            else:
                logger().warning("no speaker utterances available from assemblyai")
                return []

        except Exception as e:
            logger().error(f"error getting speaker utterances: {e}")
            return []

    def transcribe_audio_chunks(
        self,
        *,
        utterance_metadata: Sequence[Mapping[str, float | str]],
        source_language: str,
        no_dubbing_phrases: Sequence[str],
    ) -> Sequence[Mapping[str, float | str]]:
        logger().debug(f"transcribe_audio_chunks: {source_language}")
        iso_639_1 = self._get_iso_639_1(source_language)

        updated_utterance_metadata = []
        for item in utterance_metadata:
            new_item = dict(item)  # convert to dict for copying
            text = ""
            try:
                path = str(item["path"])  # ensure path is string
                duration = float(item["end"]) - float(item["start"])  # ensure numeric

                if self._is_short_audio(duration=duration):
                    text = ""
                    logger().debug(
                        f"speech_to_text._is_short_audio. audio is less than {self.MIN_SECS} second, skipping transcription of '{path}'."
                    )
                else:
                    transcribed_text = self._transcribe(
                        vocals_filepath=path, source_language_iso_639_1=iso_639_1
                    )
                    text = self._make_sure_single_space(transcribed_text)

            except Exception as e:
                logger().error(
                    f"speech_to_text.transcribe_audio_chunks. file '{path}', error: '{e}'"
                )
                text = ""

            dubbing = len(text) > 0
            logger().debug(
                f"transcribe_audio_chunks. text: '{text}' - dubbing: {dubbing}"
            )
            new_item["text"] = text
            new_item["for_dubbing"] = dubbing
            updated_utterance_metadata.append(new_item)
        return updated_utterance_metadata

    def _get_unique_speakers_largest_audio(self, utterance_metadata):
        speakers = {}
        for chunk in utterance_metadata:
            speaker_id = chunk["speaker_id"]
            length = chunk["end"] - chunk["start"]
            dubbed_path = chunk["path"]
            speaker_data = speakers.get(speaker_id, {})
            save = False
            if len(speaker_data) == 0:
                save = True
            elif length > speaker_data["length"]:
                save = True
            if save:
                speaker_data["length"] = length
                speaker_data["path"] = dubbed_path
                speakers[speaker_id] = speaker_data

        speaker_tuples = [(speaker, data["path"]) for speaker, data in speakers.items()]
        logger().debug(
            f"text_to_speech._get_unique_speakers_largest_audio: {speaker_tuples}"
        )
        return speaker_tuples

    def predict_gender(self, file, utterance_metadata):
        speaker_gender = {}
        classifier = VoiceGenderClassifier(self.device)
        speakers = self._get_unique_speakers_largest_audio(utterance_metadata)
        for speaker_id, path in speakers:
            gender = classifier.get_gender_for_file(path)
            speaker_gender[speaker_id] = gender

        r = []
        for chunk in utterance_metadata:
            speaker_id = chunk["speaker_id"]
            gender = speaker_gender[speaker_id]
            _tuple = (speaker_id, gender)
            r.append(_tuple)
        logger().debug(f"text_to_speech.diarize_speakers. returns: {r}".lower())
        return r

    def add_speaker_info(self, utterance_metadata, speaker_info):
        if len(utterance_metadata) != len(speaker_info):
            raise Exception(
                "the length of 'utterance_metadata' and 'speaker_info' must be the same."
            )

        updated_utterance_metadata = []
        for utterance, (speaker_id, gender) in zip(utterance_metadata, speaker_info):
            new_utterance = utterance.copy()
            new_utterance["speaker_id"] = speaker_id
            new_utterance["gender"] = gender
            updated_utterance_metadata.append(new_utterance)
        return updated_utterance_metadata

    def dump_transcriptions(
        self,
        *,
        output_directory: str,
        utterance_metadata: Sequence[Mapping[str, float | str]],
    ) -> None:
        output_filename = os.path.join(output_directory, "transcription.txt")

        with open(output_filename, "w") as _file:
            for utterance in utterance_metadata:
                text = str(utterance["text"])
                _file.write(text + "\n")
