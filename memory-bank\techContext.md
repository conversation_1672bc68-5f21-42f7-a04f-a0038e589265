# tech context

> this file contains the technical context for the project.

## technologies

-   python 3.11.11 (strict version requirement)
-   ffmpeg (video processing)
-   nvidia drivers (optional, for gpu acceleration)

## core libraries

-   **speech recognition**: assemblyai
-   **vocal isolation**: demucs
-   **speaker identification**: pyannote
-   **translation**: google gemini
-   **text-to-speech**:
    -   yandex speechkit (default)
    -   azure cognitive services
    -   elevenlabs

## system architecture

the system follows a pipeline architecture:

1. audio/video separation
2. vocal isolation
3. speaker diarization
4. speech segmentation
5. speech-to-text
6. translation
7. voice assignment
8. text-to-speech
9. audio merging
10. subtitle generation
11. final video assembly

## deployment considerations

-   requires multiple api keys for external services
-   gpu acceleration recommended for faster processing
-   supports incremental updates and reprocessing
